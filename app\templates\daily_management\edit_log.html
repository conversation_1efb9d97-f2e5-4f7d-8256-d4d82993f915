{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
<style>
    .form-control {
        font-size: 1rem;
    }

    label {
        font-size: 1.05rem;
        font-weight: 500;
    }

    .form-section {
        background-color: #f8f9fc;
        border-radius: 5px;
        padding: 20px;
        margin-bottom: 25px;
        border-left: 4px solid #4e73df;
    }

    .section-title {
        font-weight: 600;
        color: #4e73df;
        margin-bottom: 15px;
        font-size: 1.2rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 导入导航宏 -->
    {% from 'daily_management/components/navigation.html' import daily_management_header %}

    <!-- 显示导航和学校信息 -->
    {{ daily_management_header(title, school, log, 'daily_log') }}

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-edit mr-1"></i> {% if log %}编辑{% else %}创建{% endif %}日志信息
            </h6>
            <div>
                <span class="badge badge-info">{{ log_date.strftime('%Y-%m-%d') }}</span>
            </div>
        </div>
        <div class="card-body">
            <form method="post" id="logForm">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                <!-- 基本信息部分 -->
                <div class="form-section">
                    <div class="section-title"><i class="fas fa-info-circle mr-1"></i> 基本信息</div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="weather">天气</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-cloud-sun"></i></span>
                                    </div>
                                    <input type="text" class="form-control" id="weather" name="weather"
                                           value="{{ log.weather if log else '' }}" placeholder="请输入当日天气">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="manager">管理员</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    </div>
                                    <input type="text" class="form-control" id="manager" name="manager"
                                           value="{{ log.manager if log else '' }}" placeholder="请输入管理员姓名">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 就餐人数部分 -->
                <div class="form-section">
                    <div class="section-title"><i class="fas fa-users mr-1"></i> 就餐人数统计</div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="student_count">学生就餐人数</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-user-graduate"></i></span>
                                    </div>
                                    <input type="number" class="form-control" id="student_count" name="student_count"
                                           value="{{ log.student_count if log else 0 }}" min="0">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="teacher_count">教师就餐人数</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-chalkboard-teacher"></i></span>
                                    </div>
                                    <input type="number" class="form-control" id="teacher_count" name="teacher_count"
                                           value="{{ log.teacher_count if log else 0 }}" min="0">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="other_count">其他就餐人数</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-user-friends"></i></span>
                                    </div>
                                    <input type="number" class="form-control" id="other_count" name="other_count"
                                           value="{{ log.other_count if log else 0 }}" min="0">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle mr-1"></i> 总就餐人数:
                                <span id="total-count" class="font-weight-bold">
                                    {{ (log.student_count or 0) + (log.teacher_count or 0) + (log.other_count or 0) }}
                                </span> 人
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 菜单信息部分 -->
                <div class="form-section">
                    <div class="section-title"><i class="fas fa-utensils mr-1"></i> 菜单信息</div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="breakfast_menu">早餐菜单</label>
                                <textarea class="form-control" id="breakfast_menu" name="breakfast_menu"
                                          rows="4" placeholder="请输入早餐菜单...">{{ log.breakfast_menu if log else '' }}</textarea>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="lunch_menu">午餐菜单</label>
                                <textarea class="form-control" id="lunch_menu" name="lunch_menu"
                                          rows="4" placeholder="请输入午餐菜单...">{{ log.lunch_menu if log else '' }}</textarea>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="dinner_menu">晚餐菜单</label>
                                <textarea class="form-control" id="dinner_menu" name="dinner_menu"
                                          rows="4" placeholder="请输入晚餐菜单...">{{ log.dinner_menu if log else '' }}</textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 其他信息部分 -->
                <div class="form-section">
                    <div class="section-title"><i class="fas fa-clipboard-list mr-1"></i> 其他信息</div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="food_waste">食物浪费量(kg)</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-trash"></i></span>
                                    </div>
                                    <input type="number" step="0.01" class="form-control" id="food_waste" name="food_waste"
                                           value="{{ log.food_waste if log else 0 }}" min="0">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="form-group">
                                <label for="special_events">特殊事件概述</label>
                                <textarea class="form-control" id="special_events" name="special_events"
                                          rows="3" placeholder="请简要描述当日特殊事件...">{{ log.special_events if log else '' }}</textarea>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="operation_summary">运营总结</label>
                        <textarea class="form-control" id="operation_summary" name="operation_summary"
                                  rows="4" placeholder="请输入当日运营总结...">{{ log.operation_summary if log else '' }}</textarea>
                    </div>
                </div>

                <div class="form-group text-center mt-4">
                    <button type="submit" class="btn btn-primary btn-lg px-5">
                        <i class="fas fa-save mr-1"></i> 保存日志
                    </button>
                    <a href="{{ url_for('daily_management.logs') }}" class="btn btn-secondary btn-lg ml-2">
                        <i class="fas fa-arrow-left mr-1"></i> 返回列表
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 计算总就餐人数
        function calculateTotal() {
            var studentCount = parseInt($('#student_count').val()) || 0;
            var teacherCount = parseInt($('#teacher_count').val()) || 0;
            var otherCount = parseInt($('#other_count').val()) || 0;
            var total = studentCount + teacherCount + otherCount;
            $('#total-count').text(total);
        }

        // 监听就餐人数输入框的变化
        $('#student_count, #teacher_count, #other_count').on('input', calculateTotal);

        // 表单验证
        $('#logForm').on('submit', function(e) {
            // 显示加载状态
            $('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 保存中...');
        });

        // 初始化提示工具
        $('[data-toggle="tooltip"]').tooltip();

        // 自动调整文本区域高度
        $('textarea').each(function() {
            this.setAttribute('style', 'height:' + (this.scrollHeight) + 'px;overflow-y:hidden;');
        }).on('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });
    });
</script>
{% endblock %}
