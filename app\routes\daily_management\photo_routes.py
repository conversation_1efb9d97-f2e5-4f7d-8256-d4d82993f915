"""
照片管理相关路由
"""

import os
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from PIL import Image
from app import db
from app.models_daily_management import Photo, InspectionRecord

photo_bp = Blueprint('photo', __name__)

@photo_bp.route('/upload-inspection-photo/<int:log_id>/<string:inspection_type>', methods=['POST'])
@login_required
def upload_inspection_photo(log_id, inspection_type):
    """上传检查记录照片"""
    if 'photo' not in request.files:
        return jsonify({'error': '没有文件'}), 400
    
    photo_file = request.files['photo']
    if not photo_file.filename:
        return jsonify({'error': '没有选择文件'}), 400
    
    inspection_item = request.form.get('inspection_item')
    if not inspection_item:
        return jsonify({'error': '缺少检查项目'}), 400
    
    # 获取或创建检查记录
    inspection = InspectionRecord.query.filter_by(
        daily_log_id=log_id,
        inspection_type=inspection_type,
        inspection_item=inspection_item
    ).first()
    
    if not inspection:
        inspection = InspectionRecord(
            daily_log_id=log_id,
            inspection_type=inspection_type,
            inspection_item=inspection_item,
            status='normal',
            inspector_id=current_user.id,
            inspection_time=datetime.now()
        )
        db.session.add(inspection)
        db.session.commit()
    
    # 处理照片上传
    try:
        photo = handle_photo_upload(photo_file, 'inspection', inspection.id)
        
        return jsonify({
            'success': True,
            'id': photo.id,
            'file_path': photo.file_path,
            'message': '照片上传成功'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'上传失败: {str(e)}'}), 500

@photo_bp.route('/update-photo-rating', methods=['POST'])
@login_required
def update_photo_rating():
    """更新照片评分"""
    data = request.json
    if not data or 'photo_id' not in data or 'rating' not in data:
        return jsonify({'error': '缺少必要参数'}), 400
    
    photo_id = data['photo_id']
    rating = data['rating']
    
    if not isinstance(rating, int) or rating < 1 or rating > 5:
        return jsonify({'error': '评分必须是1-5之间的整数'}), 400
    
    photo = Photo.query.get(photo_id)
    if not photo:
        return jsonify({'error': '照片不存在'}), 404
    
    # 更新评分
    try:
        photo.rating = rating
        db.session.commit()
        return jsonify({'success': True, 'message': '评分更新成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'更新失败: {str(e)}'}), 500

@photo_bp.route('/delete-photo', methods=['POST'])
@login_required
def delete_photo():
    """删除照片"""
    data = request.json
    if not data or 'photo_id' not in data:
        return jsonify({'error': '缺少照片ID'}), 400
    
    photo_id = data['photo_id']
    photo = Photo.query.get(photo_id)
    if not photo:
        return jsonify({'error': '照片不存在'}), 404
    
    # 删除照片文件
    try:
        file_path = os.path.join(current_app.static_folder, photo.file_path.lstrip('/static/'))
        if os.path.exists(file_path):
            os.remove(file_path)
        
        # 删除数据库记录
        db.session.delete(photo)
        db.session.commit()
        
        return jsonify({'success': True, 'message': '照片删除成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'删除失败: {str(e)}'}), 500

def handle_photo_upload(photo_file, reference_type, reference_id):
    """处理照片上传"""
    if not photo_file or not photo_file.filename:
        raise ValueError('没有文件')
    
    # 确保文件名安全
    filename = secure_filename(photo_file.filename)
    
    # 生成唯一文件名
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    unique_filename = f"{timestamp}_{filename}"
    
    # 确定保存路径
    upload_folder = os.path.join(
        current_app.static_folder,
        'uploads',
        'daily_management',
        reference_type
    )
    
    # 确保目录存在
    os.makedirs(upload_folder, exist_ok=True)
    
    # 处理图片
    img = Image.open(photo_file)
    
    # 调整大小为800x600，保持宽高比
    img.thumbnail((800, 600))
    
    # 保存处理后的图片
    file_path = os.path.join(upload_folder, unique_filename)
    img.save(file_path)
    
    # 相对路径（用于数据库存储）
    relative_path = f"/static/uploads/daily_management/{reference_type}/{unique_filename}"
    
    # 创建照片记录
    photo = Photo(
        reference_id=reference_id,
        reference_type=reference_type,
        file_name=unique_filename,
        file_path=relative_path,
        rating=3,  # 默认评分为3星
        upload_time=datetime.now()
    )
    
    db.session.add(photo)
    db.session.commit()
    
    return photo
