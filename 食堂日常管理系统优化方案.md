# 食堂日常管理系统优化方案

## 1. 系统架构

### 1.1 总体架构
- 前端：HTML5 + CSS3 + JavaScript + Bootstrap 4
- 后端：Flask + SQLAlchemy
- 数据库：SQL Server
- 文档生成：WeasyPrint/wkhtmltopdf

### 1.2 模块划分
1. 仪表盘模块
2. 就餐情况与菜单管理模块
3. 检查记录管理模块
4. 陪餐记录管理模块
5. 培训记录管理模块
6. 特殊事件管理模块
7. 问题记录管理模块
8. 打印服务模块
9. 数据分析模块

## 2. 接口设计

### 2.1 通用接口规范
- 所有API返回JSON格式数据
- 统一错误处理机制
- 接口认证采用JWT或Session认证
- 分页参数统一为page和per_page

### 2.2 核心接口列表

#### 2.2.1 仪表盘接口
- `/api/dashboard/summary` - 获取仪表盘概览数据
- `/api/dashboard/recent-activities` - 获取最近活动
- `/api/dashboard/alerts` - 获取提醒和警报

#### 2.2.2 就餐情况接口
- `/api/dining/stats` - 获取就餐统计数据
- `/api/dining/menu` - 获取/更新菜单信息
- `/api/dining/trends` - 获取就餐趋势数据

#### 2.2.3 检查记录接口
- `/api/inspection/list` - 获取检查记录列表
- `/api/inspection/detail/<id>` - 获取检查详情
- `/api/inspection/create` - 创建检查记录
- `/api/inspection/update/<id>` - 更新检查记录
- `/api/inspection/stats` - 获取检查统计数据

#### 2.2.4 陪餐记录接口
- `/api/companion/list` - 获取陪餐记录列表
- `/api/companion/detail/<id>` - 获取陪餐详情
- `/api/companion/create` - 创建陪餐记录
- `/api/companion/update/<id>` - 更新陪餐记录
- `/api/companion/feedback` - 提交/获取陪餐反馈

#### 2.2.5 问题记录接口
- `/api/issue/list` - 获取问题记录列表
- `/api/issue/detail/<id>` - 获取问题详情
- `/api/issue/create` - 创建问题记录
- `/api/issue/update/<id>` - 更新问题状态
- `/api/issue/stats` - 获取问题统计数据
- `/api/issue/related/<id>` - 获取相关问题

#### 2.2.6 打印服务接口
- `/api/print/inspection/<date>` - 生成检查记录PDF
- `/api/print/companion/<date>` - 生成陪餐记录PDF
- `/api/print/issue/<id>` - 生成问题记录PDF
- `/api/print/summary/<date>` - 生成日志汇总PDF
- `/api/print/batch` - 批量生成PDF

## 3. 数据模型

### 3.1 现有数据表结构
- daily_logs - 日志主表
- inspection_records - 检查记录表
- dining_companions - 陪餐记录表
- training_records - 培训记录表
- special_events - 特殊事件表
- issue_records - 问题记录表
- photos - 照片表

### 3.2 数据关联关系
- daily_logs 1:N inspection_records
- daily_logs 1:N dining_companions
- daily_logs 1:N training_records
- daily_logs 1:N special_events
- daily_logs 1:N issue_records
- inspection_records 1:N photos
- issue_records 1:N photos
