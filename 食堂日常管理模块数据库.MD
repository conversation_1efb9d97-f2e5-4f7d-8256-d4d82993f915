# 食堂日常管理模块数据库设计文档

## 1. 数据库表概览

食堂日常管理模块包含以下主要数据表：

1. daily_logs (日常工作日志表)
2. inspection_records (检查记录表)
3. dining_companions (陪餐记录表)
4. canteen_training_records (食堂培训记录表)
5. special_events (特殊事件表)
6. issues (问题记录表)
7. photos (照片表)

## 2. 表结构详细说明

### 2.1 daily_logs (日常工作日志表)

记录食堂每日工作的基本情况。

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | Integer | 主键 | PRIMARY KEY |
| log_date | DATETIME2(1) | 日志日期 | NOT NULL, INDEX |
| weather | String(50) | 天气情况 | |
| manager | String(100) | 管理人员 | |
| student_count | Integer | 学生就餐人数 | DEFAULT 0 |
| teacher_count | Integer | 教师就餐人数 | DEFAULT 0 |
| other_count | Integer | 其他就餐人数 | DEFAULT 0 |
| breakfast_menu | Text | 早餐菜单 | |
| lunch_menu | Text | 午餐菜单 | |
| dinner_menu | Text | 晚餐菜单 | |
| food_waste | Float | 食物浪费量(kg) | |
| special_events | Text | 特殊事件概述 | |
| operation_summary | Text | 运营总结 | |
| area_id | Integer | 所属区域ID | FOREIGN KEY |
| created_by | Integer | 创建人ID | FOREIGN KEY |
| created_at | DATETIME2(1) | 创建时间 | NOT NULL |
| updated_at | DATETIME2(1) | 更新时间 | NOT NULL |

### 2.2 inspection_records (检查记录表)

记录食堂日常检查情况。

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | Integer | 主键 | PRIMARY KEY |
| daily_log_id | Integer | 关联的日志ID | FOREIGN KEY |
| inspection_type | String(10) | 检查类型(morning/noon/evening) | NOT NULL |
| inspection_item | String(100) | 检查项目 | NOT NULL |
| status | String(10) | 状态(normal/abnormal) | DEFAULT 'normal' |
| description | Text | 检查描述 | |
| photo_path | String(255) | 照片路径 | |
| inspector_id | Integer | 检查人ID | FOREIGN KEY |
| inspection_time | DATETIME2(1) | 检查时间 | NOT NULL |
| created_at | DATETIME2(1) | 创建时间 | NOT NULL |
| updated_at | DATETIME2(1) | 更新时间 | NOT NULL |

### 2.3 dining_companions (陪餐记录表)

记录食堂陪餐情况。

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | Integer | 主键 | PRIMARY KEY |
| daily_log_id | Integer | 关联的日志ID | FOREIGN KEY |
| companion_name | String(100) | 陪餐人姓名 | NOT NULL |
| companion_role | String(50) | 陪餐人角色 | NOT NULL |
| meal_type | String(20) | 餐次类型 | NOT NULL |
| dining_time | DATETIME2(1) | 陪餐时间 | NOT NULL |
| taste_rating | Integer | 口味评分 | |
| hygiene_rating | Integer | 卫生评分 | |
| service_rating | Integer | 服务评分 | |
| comments | Text | 意见建议 | |
| suggestions | Text | 改进建议 | |
| photo_paths | String(255) | 照片路径(多个用分号分隔) | |

### 2.4 canteen_training_records (食堂培训记录表)

记录食堂工作人员培训情况。

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | Integer | 主键 | PRIMARY KEY |
| daily_log_id | Integer | 关联的日志ID | FOREIGN KEY |
| training_topic | String(200) | 培训主题 | NOT NULL |
| trainer | String(100) | 培训人 | NOT NULL |
| training_time | DATETIME2(1) | 培训时间 | NOT NULL |
| location | String(100) | 培训地点 | |
| duration | Integer | 培训时长(分钟) | |
| attendees_count | Integer | 参训人数 | |
| content_summary | Text | 内容概要 | |
| effectiveness_evaluation | Text | 效果评估 | |
| photo_paths | String(255) | 照片路径(多个用分号分隔) | |
| created_at | DATETIME2(1) | 创建时间 | NOT NULL |

### 2.5 special_events (特殊事件表)

记录食堂特殊事件。

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | Integer | 主键 | PRIMARY KEY |
| daily_log_id | Integer | 关联的日志ID | FOREIGN KEY |
| event_type | String(100) | 事件类型 | NOT NULL |
| event_time | DATETIME2(1) | 事件时间 | NOT NULL |
| description | Text | 事件描述 | NOT NULL |
| participants | Text | 参与人员 | |
| handling_measures | Text | 处理措施 | |
| event_summary | Text | 事件总结 | |
| photo_paths | String(255) | 照片路径(多个用分号分隔) | |
| created_at | DATETIME2(1) | 创建时间 | NOT NULL |
| updated_at | DATETIME2(1) | 更新时间 | NOT NULL |

### 2.6 issues (问题记录表)

记录食堂问题及处理情况。

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | Integer | 主键 | PRIMARY KEY |
| daily_log_id | Integer | 关联的日志ID | FOREIGN KEY |
| issue_type | String(100) | 问题类型 | NOT NULL |
| description | Text | 问题描述 | NOT NULL |
| status | String(20) | 状态(pending/fixing/fixed) | NOT NULL |
| found_time | DATETIME2(1) | 发现时间 | NOT NULL |
| fixed_time | DATETIME2(1) | 解决时间 | |
| responsible_person | String(100) | 责任人 | |
| verification_result | Text | 验证结果 | |
| photo_paths | String(255) | 照片路径(多个用分号分隔) | |

### 2.7 photos (照片表)

存储各类记录的照片信息。

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | Integer | 主键 | PRIMARY KEY |
| reference_id | Integer | 关联记录ID | NOT NULL |
| reference_type | String | 关联类型(inspection/companion/training/event/issue) | NOT NULL |
| file_name | String(255) | 文件名 | NOT NULL |
| file_path | String(255) | 文件路径 | NOT NULL |
| description | String(255) | 照片描述 | |
| upload_time | DATETIME2(1) | 上传时间 | NOT NULL |

## 3. 表关系说明

1. daily_logs 与其他表的关系：
   - 一对多关系：
     - inspection_records (一个日志可以有多个检查记录)
     - dining_companions (一个日志可以有多个陪餐记录)
     - canteen_training_records (一个日志可以有多个培训记录)
     - special_events (一个日志可以有多个特殊事件)
     - issues (一个日志可以有多个问题记录)

2. photos 与其他表的关系：
   - 多对一关系：
     - inspection_records (通过 reference_id 和 reference_type 关联)
     - dining_companions (通过 reference_id 和 reference_type 关联)
     - canteen_training_records (通过 reference_id 和 reference_type 关联)
     - special_events (通过 reference_id 和 reference_type 关联)
     - issues (通过 reference_id 和 reference_type 关联)

## 4. 索引设计

1. daily_logs 表：
   - PRIMARY KEY (id)
   - INDEX (log_date)
   - INDEX (area_id)
   - INDEX (created_by)

2. inspection_records 表：
   - PRIMARY KEY (id)
   - INDEX (daily_log_id)
   - INDEX (inspection_type)
   - INDEX (inspector_id)

3. dining_companions 表：
   - PRIMARY KEY (id)
   - INDEX (daily_log_id)
   - INDEX (dining_time)

4. canteen_training_records 表：
   - PRIMARY KEY (id)
   - INDEX (daily_log_id)
   - INDEX (training_time)

5. special_events 表：
   - PRIMARY KEY (id)
   - INDEX (daily_log_id)
   - INDEX (event_time)

6. issues 表：
   - PRIMARY KEY (id)
   - INDEX (daily_log_id)
   - INDEX (status)
   - INDEX (found_time)

7. photos 表：
   - PRIMARY KEY (id)
   - INDEX (reference_id, reference_type)
   - INDEX (upload_time)

## 5. 时间处理说明

所有涉及时间的字段都使用 DATETIME2(1) 类型，精确到 0.1 秒，并且：

1. 在创建记录时：
   - created_at 字段由数据库自动设置为当前时间
   - 使用 datetime.now().replace(microsecond=0) 去除微秒部分

2. 在更新记录时：
   - updated_at 字段由数据库自动更新为当前时间
   - 使用 datetime.now().replace(microsecond=0) 去除微秒部分

3. 在查询时：
   - 使用 FORMAT 函数格式化日期时间显示
   - 避免直接比较时间戳，使用日期部分进行比较 

## 6. 服务层实现

### 6.1 DailyLogService (日志服务)

主要功能：
- 获取日志列表 (get_daily_logs)
- 根据ID获取日志 (get_daily_log_by_id)
- 根据日期获取日志 (get_daily_log_by_date)
- 创建日志 (create_daily_log)
- 更新日志 (update_daily_log)
- 删除日志 (delete_daily_log)
- 获取统计数据 (get_daily_statistics)

### 6.2 InspectionService (检查记录服务)

主要功能：
- 获取日志的检查记录 (get_inspections_by_daily_log)
- 根据ID获取检查记录 (get_inspection_by_id)
- 创建检查记录 (create_inspection)
- 更新检查记录 (update_inspection)
- 删除检查记录 (delete_inspection)

### 6.3 DiningCompanionService (陪餐记录服务)

主要功能：
- 获取日志的陪餐记录 (get_companions_by_daily_log)
- 根据ID获取陪餐记录 (get_companion_by_id)
- 创建陪餐记录 (create_companion)
- 更新陪餐记录 (update_companion)
- 删除陪餐记录 (delete_companion)
- 获取评价统计数据 (get_rating_statistics)

### 6.4 TrainingService (培训记录服务)

主要功能：
- 获取日志的培训记录 (get_trainings_by_daily_log)
- 根据ID获取培训记录 (get_training_by_id)
- 创建培训记录 (create_training)
- 更新培训记录 (update_training)
- 删除培训记录 (delete_training)

### 6.5 EventService (特殊事件服务)

主要功能：
- 获取日志的特殊事件 (get_events_by_daily_log)
- 根据ID获取特殊事件 (get_event_by_id)
- 创建特殊事件 (create_event)
- 更新特殊事件 (update_event)
- 删除特殊事件 (delete_event)

### 6.6 IssueService (问题记录服务)

主要功能：
- 获取区域的问题记录 (get_issues_by_area)
- 根据ID获取问题记录 (get_issue_by_id)
- 创建问题记录 (create_issue)
- 更新问题记录 (update_issue)
- 删除问题记录 (delete_issue)

### 6.7 PhotoService (照片服务)

主要功能：
- 获取引用对象的照片 (get_photos_by_reference)
- 根据ID获取照片 (get_photo_by_id)
- 保存照片 (save_photo)
- 更新照片描述 (update_photo)
- 删除照片 (delete_photo)

### 6.8 DashboardService (仪表盘服务)

主要功能：
- 获取仪表盘摘要信息 (get_dashboard_summary)
- 获取月度统计数据 (get_monthly_summary)

## 7. 服务层特点

1. 统一的错误处理：
   - 所有服务方法都包含异常处理
   - 数据库操作失败时自动回滚
   - 记录详细的错误日志

2. 活动日志记录：
   - 重要操作都会记录活动日志
   - 包含操作类型、对象和时间信息
   - 便于后期审计和追踪

3. 照片处理：
   - 统一的照片上传和存储机制
   - 支持多种引用类型
   - 自动生成唯一文件名
   - 维护文件系统和数据库的一致性

4. 数据验证：
   - 输入参数的类型和格式验证
   - 数据完整性检查
   - 业务规则验证

5. 事务管理：
   - 复杂操作使用事务确保数据一致性
   - 关联数据的级联处理
   - 出错时自动回滚

6. 权限控制：
   - 基于用户角色的访问控制
   - 区域数据隔离
   - 操作权限验证

7. 性能优化：
   - 使用原生SQL优化复杂查询
   - 合理使用索引
   - 避免N+1查询问题

8. 时间处理：
   - 统一使用DATETIME2(1)类型
   - 精确到0.1秒
   - 去除微秒部分
   - 时区处理 

## 8. 路由层实现

### 8.1 主要路由模块

1. **daily_management_bp** (`/daily-management/`)
   - 主蓝图，包含所有食堂日常管理功能
   - 注册了其他子蓝图

2. **photo_bp** (`/daily-management/photos/`)
   - 照片管理相关路由
   - 处理照片上传、删除等操作

3. **db_bp** (`/daily-management/db/`)
   - 数据库管理相关路由
   - 提供数据库维护功能

4. **image_api_bp** (`/daily-management/image-api/`)
   - 图片处理API路由
   - 提供RESTful图片服务

### 8.2 API版本

1. **API v1** (`/daily-management/api/`)
   - 基础API功能
   - 兼容性维护

2. **API v2** (`/daily-management/api/v2/`)
   - 优化的API实现
   - 使用服务层
   - 更好的错误处理
   - 标准化的响应格式

3. **Enhanced API** (`/daily-management/enhanced-api/`)
   - 增强的API功能
   - 支持批量操作
   - 高级查询功能

### 8.3 主要路由功能

1. **日志管理**
   ```
   GET  /daily-management/logs                # 日志列表
   GET  /daily-management/logs/edit/<date>    # 编辑日志
   POST /daily-management/logs/edit/<date>    # 保存日志
   ```

2. **检查记录**
   ```
   GET  /daily-management/inspections/<log_id>         # 检查记录列表
   GET  /daily-management/inspections/table/<log_id>   # 表格视图
   GET  /daily-management/inspections/card/<log_id>    # 卡片视图
   ```

3. **陪餐记录**
   ```
   GET  /daily-management/companions/<log_id>          # 陪餐记录列表
   POST /daily-management/companions/add/<log_id>      # 添加陪餐记录
   ```

4. **培训记录**
   ```
   GET  /daily-management/trainings/<log_id>          # 培训记录列表
   POST /daily-management/trainings/add/<log_id>      # 添加培训记录
   ```

5. **问题记录**
   ```
   GET  /daily-management/issues/<log_id>             # 问题记录列表
   POST /daily-management/issues/add/<log_id>         # 添加问题记录
   ```

6. **照片管理**
   ```
   POST /daily-management/photos/upload               # 上传照片
   GET  /daily-management/photos/<photo_id>          # 查看照片
   DELETE /daily-management/photos/<photo_id>        # 删除照片
   ```

### 8.4 API功能

1. **日志API**
   ```
   GET    /api/v2/daily-logs                 # 获取日志列表
   GET    /api/v2/daily-logs/<id>           # 获取日志详情
   POST   /api/v2/daily-logs                # 创建日志
   PUT    /api/v2/daily-logs/<id>           # 更新日志
   DELETE /api/v2/daily-logs/<id>           # 删除日志
   ```

2. **照片API**
   ```
   GET    /api/v2/photos/<type>/<id>        # 获取照片列表
   POST   /api/v2/photos/<type>/<id>        # 上传照片
   DELETE /api/v2/photos/<id>               # 删除照片
   ```

3. **统计API**
   ```
   GET /api/v2/statistics/daily             # 日志统计
   GET /api/v2/statistics/inspection        # 检查统计
   GET /api/v2/statistics/photo             # 照片统计
   ```

4. **仪表盘API**
   ```
   GET /api/v2/dashboard/summary            # 仪表盘摘要
   GET /api/v2/dashboard/weekly             # 周统计
   GET /api/v2/dashboard/monthly            # 月统计
   ```

### 8.5 路由特点

1. **权限控制**
   - 所有路由都需要登录
   - 管理员路由使用 @admin_required 装饰器
   - 基于用户角色的访问控制

2. **数据验证**
   - 请求参数验证
   - 文件上传验证
   - 数据类型转换

3. **错误处理**
   - 统一的错误响应格式
   - 详细的错误信息
   - 日志记录

4. **响应格式**
   - JSON格式的API响应
   - 标准化的成功/失败标识
   - 分页支持

5. **文件处理**
   - 安全的文件名处理
   - 图片压缩和优化
   - 文件存储路径管理

6. **性能优化**
   - 缓存支持
   - 延迟加载
   - 查询优化

7. **安全特性**
   - CSRF保护
   - XSS防护
   - SQL注入防护 