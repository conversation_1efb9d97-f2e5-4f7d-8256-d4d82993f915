"""
检查记录二维码相关路由

提供检查记录二维码生成和处理功能。
"""

from flask import render_template, redirect, url_for, flash, request, current_app, abort, jsonify
from flask_login import login_required, current_user
from app import db
from app.routes.daily_management import daily_management_bp
from app.models_daily_management import DailyLog, InspectionRecord
from app.models import AdministrativeArea
from app.utils.qrcode_helper import generate_qrcode_base64
from sqlalchemy import text
from datetime import datetime, date, timedelta
import os

# 生成检查记录二维码
@daily_management_bp.route('/inspections/qrcode/<int:log_id>/<inspection_type>')
@login_required
def generate_inspection_qrcode(log_id, inspection_type):
    """生成检查记录二维码"""
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法生成二维码', 'danger')
        return redirect(url_for('main.index'))

    # 获取日志
    log = DailyLog.query.filter_by(id=log_id, area_id=user_area.id).first_or_404()

    # 验证检查类型
    if inspection_type not in ['morning', 'noon', 'evening']:
        flash('检查类型无效', 'danger')
        return redirect(url_for('daily_management.inspections', log_id=log_id))

    try:
        # 生成上传照片的URL（员工用）
        upload_url = url_for('daily_management.public_upload_inspection_photo',
                            school_id=user_area.id,
                            log_id=log_id,
                            inspection_type=inspection_type,
                            _external=True)
        upload_qrcode_base64 = generate_qrcode_base64(upload_url)

        # 生成评分页面的URL（管理员用）
        rate_url = url_for('daily_management.public_rate_inspection_photos',
                           school_id=user_area.id,
                           log_id=log_id,
                           inspection_type=inspection_type,
                           _external=True)
        rate_qrcode_base64 = generate_qrcode_base64(rate_url)

        # 获取检查类型名称
        inspection_type_name = {
            'morning': '晨检',
            'noon': '午检',
            'evening': '晚检'
        }.get(inspection_type, '检查')

        return render_template('daily_management/inspection_qrcode.html',
                              title=f'{user_area.name} - {inspection_type_name}二维码',
                              log=log,
                              school=user_area,
                              inspection_type=inspection_type,
                              inspection_type_name=inspection_type_name,
                              upload_qrcode_base64=upload_qrcode_base64,
                              upload_url=upload_url,
                              rate_qrcode_base64=rate_qrcode_base64,
                              rate_url=rate_url)
    except Exception as e:
        current_app.logger.error(f"生成检查记录二维码失败: {str(e)}")
        flash(f'生成二维码失败: {str(e)}', 'danger')
        return redirect(url_for('daily_management.inspections', log_id=log_id))

# 公开访问的照片上传页面
@daily_management_bp.route('/public/inspections/upload/<int:school_id>/<int:log_id>/<inspection_type>')
def public_upload_inspection_photo(school_id, log_id, inspection_type):
    """公开访问的检查记录照片上传页面"""
    # 获取学校信息
    school = AdministrativeArea.query.get_or_404(school_id)

    # 获取日志
    log = DailyLog.query.filter_by(id=log_id, area_id=school_id).first_or_404()

    # 验证检查类型
    if inspection_type not in ['morning', 'noon', 'evening']:
        flash('检查类型无效', 'danger')
        return redirect(url_for('main.index'))

    # 获取检查项目列表
    inspection_items = ['地面卫生', '操作台卫生', '设备卫生', '食材存储', '人员卫生', '餐具消毒']

    # 获取检查类型名称
    inspection_type_name = {
        'morning': '晨检',
        'noon': '午检',
        'evening': '晚检'
    }.get(inspection_type, '检查')

    return render_template('daily_management/public_upload_inspection_photo.html',
                          title=f'{school.name} - 上传{inspection_type_name}照片',
                          school=school,
                          log=log,
                          inspection_type=inspection_type,
                          inspection_type_name=inspection_type_name,
                          inspection_items=inspection_items)

# 扫码上传入口页面
@daily_management_bp.route('/scan-upload')
@login_required
def scan_upload_entry():
    """扫码上传入口页面 - 显示今日检查记录的二维码"""
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法使用此功能', 'danger')
        return redirect(url_for('main.index'))

    # 获取今天的日期
    today = datetime.now().date()

    # 查找今天的日志
    log = DailyLog.query.filter_by(log_date=today, area_id=user_area.id).first()

    # 如果今天没有日志，自动创建一个
    if not log:
        try:
            log = DailyLog(
                log_date=today,
                area_id=user_area.id,
                creator_id=current_user.id,
                weather='晴',
                temperature='25℃',
                status='normal'
            )
            db.session.add(log)
            db.session.commit()
            flash('已自动创建今日工作日志', 'success')
        except Exception as e:
            db.session.rollback()
            flash(f'创建日志失败: {str(e)}', 'danger')
            return redirect(url_for('daily_management.index'))

    # 生成三种检查类型的二维码
    try:
        # 晨检二维码
        morning_url = url_for('daily_management.public_upload_inspection_photo',
                             school_id=user_area.id,
                             log_id=log.id,
                             inspection_type='morning',
                             _external=True)
        morning_qrcode = generate_qrcode_base64(morning_url)

        # 午检二维码
        noon_url = url_for('daily_management.public_upload_inspection_photo',
                          school_id=user_area.id,
                          log_id=log.id,
                          inspection_type='noon',
                          _external=True)
        noon_qrcode = generate_qrcode_base64(noon_url)

        # 晚检二维码
        evening_url = url_for('daily_management.public_upload_inspection_photo',
                             school_id=user_area.id,
                             log_id=log.id,
                             inspection_type='evening',
                             _external=True)
        evening_qrcode = generate_qrcode_base64(evening_url)

        return render_template('daily_management/scan_upload_entry.html',
                              title='扫码上传入口',
                              school=user_area,
                              log=log,
                              morning_qrcode=morning_qrcode,
                              noon_qrcode=noon_qrcode,
                              evening_qrcode=evening_qrcode)
    except Exception as e:
        flash(f'生成二维码失败: {str(e)}', 'danger')
        return redirect(url_for('daily_management.index'))

# 公开访问的照片评分页面
@daily_management_bp.route('/public/inspections/rate/<int:school_id>/<int:log_id>/<inspection_type>')
def public_rate_inspection_photos(school_id, log_id, inspection_type):
    """公开访问的检查记录照片评分页面"""
    # 获取学校信息
    school = AdministrativeArea.query.get_or_404(school_id)

    # 获取日志
    log = DailyLog.query.filter_by(id=log_id, area_id=school_id).first_or_404()

    # 验证检查类型
    if inspection_type not in ['morning', 'noon', 'evening']:
        flash('检查类型无效', 'danger')
        return redirect(url_for('main.index'))

    # 获取检查记录
    inspections = InspectionRecord.query.filter_by(
        daily_log_id=log_id,
        inspection_type=inspection_type
    ).all()

    # 获取照片
    photos_by_item = {}
    for inspection in inspections:
        # 使用原始SQL查询获取照片
        sql = text("""
        SELECT id, file_path, description, rating, upload_time
        FROM photos
        WHERE reference_type = 'inspection' AND reference_id = :reference_id
        ORDER BY upload_time DESC
        """)

        result = db.session.execute(sql, {'reference_id': inspection.id})

        photos = []
        for row in result:
            photos.append({
                'id': row[0],
                'file_path': row[1],
                'description': row[2],
                'rating': row[3],
                'upload_time': row[4]
            })

        if photos:
            photos_by_item[inspection.inspection_item] = photos

    # 获取检查类型名称
    inspection_type_name = {
        'morning': '晨检',
        'noon': '午检',
        'evening': '晚检'
    }.get(inspection_type, '检查')

    return render_template('daily_management/public_rate_inspection_photos.html',
                          title=f'{school.name} - 评价{inspection_type_name}照片',
                          school=school,
                          log=log,
                          inspection_type=inspection_type,
                          inspection_type_name=inspection_type_name,
                          photos_by_item=photos_by_item)
