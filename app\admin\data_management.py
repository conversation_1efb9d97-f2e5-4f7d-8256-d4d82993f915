"""
数据管理模块 - 提供数据清空等功能
"""

from flask import current_app
from app import db
from app.models import (
    User, Role, UserRole, AdministrativeArea,
    Ingredient, IngredientCategory, Recipe, RecipeIngredient,
    Supplier, SupplierCategory, SupplierProduct, SupplierCertificate,
    Inventory, StockIn, StockOut, StorageLocation, Warehouse,
    MenuPlan, MenuRecipe, FoodSample,
    Employee, HealthCertificate, MedicalExamination, DailyHealthCheck, TrainingRecord,
    AuditLog, Notification
)
# 已删除食堂日常管理模块相关导入
from sqlalchemy import text
import json
from datetime import datetime

class DataManagementService:
    """数据管理服务类"""

    @staticmethod
    def clear_ingredient_data(include_categories=0):
        """
        清空食材数据

        Args:
            include_categories: 是否同时清空食材分类

        Returns:
            dict: 包含操作结果的字典
        """
        try:
            # 记录清空前的数据量
            ingredient_count = Ingredient.query.count()
            category_count = IngredientCategory.query.count() if include_categories else 0

            # 清空食材相关的关联数据
            RecipeIngredient.query.delete()

            # 清空库存相关数据
            StockOut.query.delete()
            StockIn.query.delete()
            Inventory.query.delete()

            # 清空食材数据
            Ingredient.query.delete()

            # 如果需要，清空食材分类
            if include_categories:
                IngredientCategory.query.delete()

            # 提交事务
            db.session.commit()

            return {
                'success': 1,
                'message': '食材数据清空成功',
                'details': {
                    'ingredients_removed': ingredient_count,
                    'categories_removed': category_count if include_categories else 0
                }
            }
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"清空食材数据失败: {str(e)}")
            return {
                'success': 0,
                'message': f'清空食材数据失败: {str(e)}'
            }

    @staticmethod
    def clear_recipe_data():
        """
        清空食谱数据

        Returns:
            dict: 包含操作结果的字典
        """
        try:
            # 记录清空前的数据量
            recipe_count = Recipe.query.count()
            recipe_ingredient_count = RecipeIngredient.query.count()

            # 清空食谱相关的关联数据
            MenuRecipe.query.delete()
            RecipeIngredient.query.delete()

            # 清空食谱数据
            Recipe.query.delete()

            # 提交事务
            db.session.commit()

            return {
                'success': 1,
                'message': '食谱数据清空成功',
                'details': {
                    'recipes_removed': recipe_count,
                    'recipe_ingredients_removed': recipe_ingredient_count
                }
            }
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"清空食谱数据失败: {str(e)}")
            return {
                'success': 0,
                'message': f'清空食谱数据失败: {str(e)}'
            }

    @staticmethod
    def clear_supplier_data(include_categories=0):
        """
        清空供应商数据

        Args:
            include_categories: 是否同时清空供应商分类

        Returns:
            dict: 包含操作结果的字典
        """
        try:
            # 记录清空前的数据量
            supplier_count = Supplier.query.count()
            product_count = SupplierProduct.query.count()
            certificate_count = SupplierCertificate.query.count()
            category_count = SupplierCategory.query.count() if include_categories else 0

            # 清空供应商相关的关联数据
            SupplierCertificate.query.delete()
            SupplierProduct.query.delete()

            # 清空供应商数据
            Supplier.query.delete()

            # 如果需要，清空供应商分类
            if include_categories:
                SupplierCategory.query.delete()

            # 提交事务
            db.session.commit()

            return {
                'success': 1,
                'message': '供应商数据清空成功',
                'details': {
                    'suppliers_removed': supplier_count,
                    'products_removed': product_count,
                    'certificates_removed': certificate_count,
                    'categories_removed': category_count if include_categories else 0
                }
            }
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"清空供应商数据失败: {str(e)}")
            return {
                'success': 0,
                'message': f'清空供应商数据失败: {str(e)}'
            }

    @staticmethod
    def clear_inventory_data(include_locations=0):
        """
        清空库存数据

        Args:
            include_locations: 是否同时清空仓库和存储位置

        Returns:
            dict: 包含操作结果的字典
        """
        try:
            # 记录清空前的数据量
            inventory_count = Inventory.query.count()
            stock_in_count = StockIn.query.count()
            stock_out_count = StockOut.query.count()
            location_count = StorageLocation.query.count() if include_locations else 0
            warehouse_count = Warehouse.query.count() if include_locations else 0

            # 清空库存相关数据
            StockOut.query.delete()
            StockIn.query.delete()
            Inventory.query.delete()

            # 如果需要，清空仓库和存储位置
            if include_locations:
                StorageLocation.query.delete()
                Warehouse.query.delete()

            # 提交事务
            db.session.commit()

            return {
                'success': 1,
                'message': '库存数据清空成功',
                'details': {
                    'inventory_records_removed': inventory_count,
                    'stock_in_records_removed': stock_in_count,
                    'stock_out_records_removed': stock_out_count,
                    'storage_locations_removed': location_count if include_locations else 0,
                    'warehouses_removed': warehouse_count if include_locations else 0
                }
            }
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"清空库存数据失败: {str(e)}")
            return {
                'success': 0,
                'message': f'清空库存数据失败: {str(e)}'
            }

    @staticmethod
    def clear_menu_data():
        """
        清空菜单计划数据

        Returns:
            dict: 包含操作结果的字典
        """
        try:
            # 记录清空前的数据量
            menu_plan_count = MenuPlan.query.count()
            menu_recipe_count = MenuRecipe.query.count()

            # 清空菜单相关数据
            MenuRecipe.query.delete()
            MenuPlan.query.delete()

            # 提交事务
            db.session.commit()

            return {
                'success': 1,
                'message': '菜单计划数据清空成功',
                'details': {
                    'menu_plans_removed': menu_plan_count,
                    'menu_recipes_removed': menu_recipe_count
                }
            }
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"清空菜单计划数据失败: {str(e)}")
            return {
                'success': 0,
                'message': f'清空菜单计划数据失败: {str(e)}'
            }

    @staticmethod
    def clear_food_sample_data():
        """
        清空留样数据

        Returns:
            dict: 包含操作结果的字典
        """
        try:
            # 记录清空前的数据量
            sample_count = FoodSample.query.count()

            # 清空留样数据
            FoodSample.query.delete()

            # 提交事务
            db.session.commit()

            return {
                'success': 1,
                'message': '留样数据清空成功',
                'details': {
                    'samples_removed': sample_count
                }
            }
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"清空留样数据失败: {str(e)}")
            return {
                'success': 0,
                'message': f'清空留样数据失败: {str(e)}'
            }

    @staticmethod
    def clear_employee_data():
        """
        清空员工数据

        Returns:
            dict: 包含操作结果的字典
        """
        try:
            # 记录清空前的数据量
            employee_count = Employee.query.count()
            health_cert_count = HealthCertificate.query.count()
            medical_exam_count = MedicalExamination.query.count()
            health_check_count = DailyHealthCheck.query.count()
            training_record_count = TrainingRecord.query.count()

            # 清空员工相关数据
            TrainingRecord.query.delete()
            DailyHealthCheck.query.delete()
            MedicalExamination.query.delete()
            HealthCertificate.query.delete()
            Employee.query.delete()

            # 提交事务
            db.session.commit()

            return {
                'success': 1,
                'message': '员工数据清空成功',
                'details': {
                    'employees_removed': employee_count,
                    'health_certificates_removed': health_cert_count,
                    'medical_examinations_removed': medical_exam_count,
                    'health_checks_removed': health_check_count,
                    'training_records_removed': training_record_count
                }
            }
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"清空员工数据失败: {str(e)}")
            return {
                'success': 0,
                'message': f'清空员工数据失败: {str(e)}'
            }

    @staticmethod
    def clear_notification_data():
        """
        清空通知数据

        Returns:
            dict: 包含操作结果的字典
        """
        try:
            # 记录清空前的数据量
            notification_count = Notification.query.count()

            # 清空通知数据
            Notification.query.delete()

            # 提交事务
            db.session.commit()

            return {
                'success': 1,
                'message': '通知数据清空成功',
                'details': {
                    'notifications_removed': notification_count
                }
            }
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"清空通知数据失败: {str(e)}")
            return {
                'success': 0,
                'message': f'清空通知数据失败: {str(e)}'
            }

    @staticmethod
    def clear_audit_logs():
        """
        清空审计日志

        Returns:
            dict: 包含操作结果的字典
        """
        try:
            # 记录清空前的数据量
            log_count = AuditLog.query.count()

            # 清空审计日志
            AuditLog.query.delete()

            # 提交事务
            db.session.commit()

            return {
                'success': 1,
                'message': '审计日志清空成功',
                'details': {
                    'logs_removed': log_count
                }
            }
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"清空审计日志失败: {str(e)}")
            return {
                'success': 0,
                'message': f'清空审计日志失败: {str(e)}'
            }

    @staticmethod
    def get_database_statistics():
        """
        获取数据库统计信息

        Returns:
            dict: 包含各模块数据统计的字典
        """
        try:
            stats = {
                'users': User.query.count(),
                'roles': Role.query.count(),
                'areas': AdministrativeArea.query.count(),
                'ingredients': {
                    'total': Ingredient.query.count(),
                    'categories': IngredientCategory.query.count()
                },
                'recipes': {
                    'total': Recipe.query.count(),
                    'ingredients': RecipeIngredient.query.count()
                },
                'suppliers': {
                    'total': Supplier.query.count(),
                    'products': SupplierProduct.query.count(),
                    'certificates': SupplierCertificate.query.count(),
                    'categories': SupplierCategory.query.count()
                },
                'inventory': {
                    'records': Inventory.query.count(),
                    'stock_in': StockIn.query.count(),
                    'stock_out': StockOut.query.count(),
                    'warehouses': Warehouse.query.count(),
                    'locations': StorageLocation.query.count()
                },
                'menu': {
                    'plans': MenuPlan.query.count(),
                    'recipes': MenuRecipe.query.count()
                },
                'food_samples': FoodSample.query.count(),
                'employees': {
                    'total': Employee.query.count(),
                    'health_certificates': HealthCertificate.query.count(),
                    'medical_examinations': MedicalExamination.query.count(),
                    'health_checks': DailyHealthCheck.query.count(),
                    'training_records': TrainingRecord.query.count()
                },
                'notifications': Notification.query.count(),
                'audit_logs': AuditLog.query.count()
            }

            return {
                'success': 1,
                'statistics': stats
            }
        except Exception as e:
            current_app.logger.error(f"获取数据库统计信息失败: {str(e)}")
            return {
                'success': 0,
                'message': f'获取数据库统计信息失败: {str(e)}'
            }
