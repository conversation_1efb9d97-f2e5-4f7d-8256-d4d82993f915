<!-- 检查项详情展示模板 -->
<div class="inspection-display">
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-clipboard-check"></i> {{ type_name }}：{{ inspection.inspection_item }}
            </h6>
            <div class="text-muted small">
                {{ inspection.inspection_time|format_datetime if inspection.inspection_time else '未记录时间' }}
                {% if inspection.inspector %}
                <span class="ml-2">检查人：{{ inspection.inspector.name }}</span>
                {% endif %}
            </div>
        </div>
        <div class="card-body">
            <!-- 检查状态和描述 -->
            <div class="mb-3">
                <div class="d-flex align-items-center mb-2">
                    <span class="badge badge-{{ 'success' if inspection.status == 'normal' else 'danger' }} mr-2">
                        {{ '正常' if inspection.status == 'normal' else '异常' }}
                    </span>
                    <div class="flex-grow-1">
                        {% if inspection.description %}
                        <p class="mb-0">{{ inspection.description }}</p>
                        {% else %}
                        <p class="text-muted mb-0">无描述</p>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- 照片展示 -->
            {% if photos %}
            <div class="photo-gallery">
                <h6 class="font-weight-bold text-primary mb-3">
                    <i class="fas fa-camera"></i> 照片记录
                    {% if photos|length > 0 %}
                    <span class="badge badge-primary ml-2">{{ photos|length }}张</span>
                    {% endif %}
                </h6>
                
                <div class="row">
                    {% for photo in photos %}
                    <div class="col-md-4 col-sm-6 mb-3">
                        <div class="card h-100">
                            <a href="{{ photo.file_path }}" target="_blank" class="photo-link">
                                <img src="{{ photo.file_path }}" class="card-img-top" alt="检查照片">
                            </a>
                            <div class="card-body p-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="rating">
                                        {% for i in range(1, 6) %}
                                        <i class="fas fa-star {{ 'text-warning' if i <= photo.rating else 'text-muted' }}"></i>
                                        {% endfor %}
                                    </div>
                                    <small class="text-muted">{{ photo.created_at|format_datetime }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% else %}
            <div class="alert alert-info mb-0">
                <i class="fas fa-info-circle"></i> 暂无照片记录
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 照片查看器样式 -->
<style>
    .inspection-display .photo-link {
        display: block;
        height: 200px;
        overflow: hidden;
        position: relative;
    }
    
    .inspection-display .photo-link img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }
    
    .inspection-display .photo-link:hover img {
        transform: scale(1.05);
    }
    
    .inspection-display .rating {
        font-size: 0.9rem;
    }
</style>

<!-- 照片查看器脚本 -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化照片点击放大功能
        const photoLinks = document.querySelectorAll('.inspection-display .photo-link');
        photoLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const imgSrc = this.getAttribute('href');
                
                // 使用 SweetAlert2 显示大图
                if (typeof Swal !== 'undefined') {
                    Swal.fire({
                        imageUrl: imgSrc,
                        imageAlt: '检查照片',
                        showCloseButton: true,
                        showConfirmButton: false,
                        width: 'auto',
                        padding: '1rem',
                        background: '#fff',
                        backdrop: 'rgba(0,0,0,0.8)'
                    });
                } else {
                    // 如果没有 SweetAlert2，则在新窗口打开
                    window.open(imgSrc, '_blank');
                }
            });
        });
    });
</script>
