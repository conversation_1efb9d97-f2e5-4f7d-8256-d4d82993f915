"""
仪表盘API路由

提供仪表盘API接口，用于前端调用。
"""

from flask import Blueprint, jsonify, request
from flask_login import login_required, current_user
# 已删除食堂日常管理模块相关导入
from app import db
from sqlalchemy import desc
from datetime import datetime

# 创建蓝图
dashboard_api_bp = Blueprint('dashboard_api', __name__)

@dashboard_api_bp.route('/api/v2/dashboard/summary', methods=['GET'])
@login_required
def api_v2_dashboard_summary():
    """获取仪表盘摘要"""
    # 食堂日常管理模块已删除，返回空摘要
    return jsonify({
        'message': '食堂日常管理模块已删除',
        'data': {}
    })

@dashboard_api_bp.route('/api/v2/dashboard/weekly', methods=['GET'])
@login_required
def api_v2_weekly_summary():
    """获取周摘要"""
    week_start = request.args.get('week_start')
    area_id = request.args.get('area_id', type=int)

    summary = DashboardService.get_weekly_summary(week_start, area_id)
    return jsonify(summary)

@dashboard_api_bp.route('/api/v2/dashboard/monthly', methods=['GET'])
@login_required
def api_v2_monthly_summary():
    """获取月摘要"""
    year = request.args.get('year', type=int)
    month = request.args.get('month', type=int)
    area_id = request.args.get('area_id', type=int)

    summary = DashboardService.get_monthly_summary(year, month, area_id)
    return jsonify(summary)

@dashboard_api_bp.route('/api/v2/dining-companions/recent', methods=['GET'])
@login_required
def api_v2_recent_dining_companions():
    """获取最近的陪餐记录"""
    # 食堂日常管理模块已删除，返回空列表
    return jsonify([])

    # 构建响应数据
    result = []
    for companion in companions:
        # 获取关联的日志
        log = DailyLog.query.get(companion.daily_log_id) if companion.daily_log_id else None

        # 检查是否有照片
        has_photo = Photo.query.filter_by(
            reference_id=companion.id,
            reference_type='companion'
        ).count() > 0

        # 构建记录数据
        record = {
            'id': companion.id,
            'name': companion.companion_name,
            'role': companion.companion_role,
            'time': companion.dining_time.strftime('%H:%M') if companion.dining_time else '',
            'date': log.log_date.strftime('%Y-%m-%d') if log and log.log_date else '',
            'meal_type': companion.meal_type,
            'comments': companion.comments,
            'has_photo': has_photo,
            'area_name': log.area.name if log and hasattr(log, 'area') and log.area else '未知区域'
        }
        result.append(record)

    return jsonify(result)
