{% extends "base.html" %}

{% block title %}日常管理{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .overview-card {
        border-radius: 10px;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        transition: transform 0.3s;
    }

    .overview-card:hover {
        transform: translateY(-5px);
    }

    .overview-card .title {
        font-size: 0.9rem;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.8);
    }

    .overview-card .count {
        font-size: 1.8rem;
        font-weight: 700;
        margin-top: 5px;
    }

    .overview-card .icon {
        font-size: 2rem;
        opacity: 0.8;
    }

    .feature-card {
        transition: transform 0.3s;
        height: 100%;
    }

    .feature-card:hover {
        transform: translateY(-5px);
    }

    .icon-box {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 15px;
    }

    .status-badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
        border-radius: 10px;
        font-weight: 600;
    }

    .status-badge-success {
        background-color: rgba(40, 167, 69, 0.1);
        color: #28a745;
    }

    .status-badge-warning {
        background-color: rgba(255, 193, 7, 0.1);
        color: #ffc107;
    }

    .status-badge-info {
        background-color: rgba(23, 162, 184, 0.1);
        color: #17a2b8;
    }

    .status-badge-danger {
        background-color: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }

    .issue-list {
        max-height: 400px;
        overflow-y: auto;
    }

    .issue-item {
        padding: 10px;
        border-radius: 5px;
        margin-bottom: 10px;
        border-left: 4px solid #6c757d;
        background-color: #f8f9fa;
    }

    .issue-item.priority-high {
        border-left-color: #dc3545;
    }

    .issue-item.priority-medium {
        border-left-color: #ffc107;
    }

    .issue-item.priority-low {
        border-left-color: #28a745;
    }

    .issue-title {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .issue-meta {
        font-size: 0.8rem;
        color: #6c757d;
    }

    .chart-container {
        position: relative;
        margin: auto;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">日常管理</h1>
        <div>
            <a href="{{ url_for('daily_management.print_daily_summary', date=today.strftime('%Y-%m-%d')) if today_log else '#' }}"
               class="btn btn-primary print-btn {{ 'disabled' if not today_log else '' }}"
               {{ 'disabled' if not today_log else '' }}>
                <i class="fas fa-print"></i> 打印今日汇总
            </a>
        </div>
    </div>

    <!-- 快速入口卡片 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-tachometer-alt mr-1"></i> 快速入口
            </h6>
        </div>
        <div class="card-body">
            <div class="text-center mb-4">
                <a href="{{ url_for('daily_management.edit_log', date_str=today.strftime('%Y-%m-%d')) }}" class="btn btn-primary btn-lg">
                    <i class="fas fa-calendar-day mr-1"></i> 进入今日日志
                </a>
                <p class="text-muted mt-2">管理日志、检查记录、陪餐记录等所有日常工作</p>
            </div>

            <!-- 主要功能模块 -->
            <div class="row">
                <div class="col-md-3 mb-3">
                    <div class="card feature-card border-left-primary">
                        <div class="card-body">
                            <div class="icon-box bg-primary text-white mx-auto">
                                <i class="fas fa-clipboard-check"></i>
                            </div>
                            <h6 class="text-center font-weight-bold">检查记录</h6>
                            <div class="text-center">
                                <a href="{{ url_for('daily_management.auto_inspections') }}" class="btn btn-sm btn-primary mb-1">
                                    <i class="fas fa-plus mr-1"></i> 快速检查
                                </a>
                            </div>
                            <div class="text-center">
                                <small class="text-muted">晨检、午检、晚检记录</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-3 mb-3">
                    <div class="card feature-card border-left-success">
                        <div class="card-body">
                            <div class="icon-box bg-success text-white mx-auto">
                                <i class="fas fa-utensils"></i>
                            </div>
                            <h6 class="text-center font-weight-bold">陪餐记录</h6>
                            <div class="text-center">
                                <a href="{{ url_for('daily_management.auto_companions') }}" class="btn btn-sm btn-success mb-1">
                                    <i class="fas fa-plus mr-1"></i> 添加陪餐
                                </a>
                            </div>
                            <div class="text-center">
                                <small class="text-muted">陪餐人员记录和评价</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-3 mb-3">
                    <div class="card feature-card border-left-info">
                        <div class="card-body">
                            <div class="icon-box bg-info text-white mx-auto">
                                <i class="fas fa-chalkboard-teacher"></i>
                            </div>
                            <h6 class="text-center font-weight-bold">培训记录</h6>
                            <div class="text-center">
                                <a href="{{ url_for('daily_management.auto_trainings') }}" class="btn btn-sm btn-info mb-1">
                                    <i class="fas fa-plus mr-1"></i> 添加培训
                                </a>
                            </div>
                            <div class="text-center">
                                <small class="text-muted">食堂培训活动记录</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-3 mb-3">
                    <div class="card feature-card border-left-warning">
                        <div class="card-body">
                            <div class="icon-box bg-warning text-white mx-auto">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <h6 class="text-center font-weight-bold">问题记录</h6>
                            <div class="text-center">
                                <a href="{{ url_for('daily_management.auto_issues') }}" class="btn btn-sm btn-warning mb-1">
                                    <i class="fas fa-plus mr-1"></i> 记录问题
                                </a>
                            </div>
                            <div class="text-center">
                                <small class="text-muted">问题发现和处理跟踪</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第二行功能 -->
            <div class="row">
                <div class="col-md-3 mb-3">
                    <div class="card feature-card border-left-danger">
                        <div class="card-body">
                            <div class="icon-box bg-danger text-white mx-auto">
                                <i class="fas fa-calendar-times"></i>
                            </div>
                            <h6 class="text-center font-weight-bold">特殊事件</h6>
                            <div class="text-center">
                                <a href="{{ url_for('daily_management.auto_events') }}" class="btn btn-sm btn-danger mb-1">
                                    <i class="fas fa-plus mr-1"></i> 记录事件
                                </a>
                            </div>
                            <div class="text-center">
                                <small class="text-muted">突发事件、参观访问等</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-3 mb-3">
                    <div class="card feature-card border-left-secondary">
                        <div class="card-body">
                            <div class="icon-box bg-secondary text-white mx-auto">
                                <i class="fas fa-camera"></i>
                            </div>
                            <h6 class="text-center font-weight-bold">照片管理</h6>
                            <div class="text-center">
                                {% if today_log %}
                                <a href="{{ url_for('daily_management.rate_inspection_photos', log_id=today_log.id) }}" class="btn btn-sm btn-secondary mb-1">
                                    <i class="fas fa-star mr-1"></i> 评价照片
                                </a>
                                {% else %}
                                <span class="btn btn-sm btn-secondary mb-1 disabled">
                                    <i class="fas fa-star mr-1"></i> 评价照片
                                </span>
                                {% endif %}
                            </div>
                            <div class="text-center">
                                <small class="text-muted">检查照片上传和评价</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-3 mb-3">
                    <div class="card feature-card border-left-dark">
                        <div class="card-body">
                            <div class="icon-box bg-dark text-white mx-auto">
                                <i class="fas fa-qrcode"></i>
                            </div>
                            <h6 class="text-center font-weight-bold">二维码</h6>
                            <div class="text-center">
                                <a href="{{ url_for('daily_management.school_qrcode') }}" class="btn btn-sm btn-dark mb-1">
                                    <i class="fas fa-qrcode mr-1"></i> 生成二维码
                                </a>
                            </div>
                            <div class="text-center">
                                <small class="text-muted">陪餐记录公开访问</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-3 mb-3">
                    <div class="card feature-card border-left-primary">
                        <div class="card-body">
                            <div class="icon-box bg-primary text-white mx-auto">
                                <i class="fas fa-cog"></i>
                            </div>
                            <h6 class="text-center font-weight-bold">检查模板</h6>
                            <div class="text-center">
                                <a href="{{ url_for('daily_management.inspection_templates') }}" class="btn btn-sm btn-primary mb-1">
                                    <i class="fas fa-list mr-1"></i> 管理模板
                                </a>
                            </div>
                            <div class="text-center">
                                <small class="text-muted">检查项目模板管理</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 检查记录视图模式 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-success">
                <i class="fas fa-eye mr-1"></i> 检查记录视图模式
            </h6>
        </div>
        <div class="card-body">
            <p class="text-muted mb-3">选择不同的视图模式来查看检查记录</p>
            <div class="row">
                <div class="col-md-2 mb-2">
                    {% if today_log %}
                    <a href="{{ url_for('daily_management.simplified_inspection', log_id=today_log.id) }}" class="btn btn-outline-success btn-sm btn-block">
                        <i class="fas fa-th-large mr-1"></i> 简化视图
                    </a>
                    {% else %}
                    <a href="{{ url_for('daily_management.inspections_by_date', date_str=today.strftime('%Y-%m-%d'), view='simplified') }}" class="btn btn-outline-success btn-sm btn-block">
                        <i class="fas fa-th-large mr-1"></i> 简化视图
                    </a>
                    {% endif %}
                </div>
                <div class="col-md-2 mb-2">
                    {% if today_log %}
                    <a href="{{ url_for('daily_management.inspections_table', log_id=today_log.id) }}" class="btn btn-outline-primary btn-sm btn-block">
                        <i class="fas fa-table mr-1"></i> 表格视图
                    </a>
                    {% else %}
                    <a href="{{ url_for('daily_management.inspections_by_date', date_str=today.strftime('%Y-%m-%d'), view='table') }}" class="btn btn-outline-primary btn-sm btn-block">
                        <i class="fas fa-table mr-1"></i> 表格视图
                    </a>
                    {% endif %}
                </div>
                <div class="col-md-2 mb-2">
                    {% if today_log %}
                    <a href="{{ url_for('daily_management.inspections_card_layout', log_id=today_log.id) }}" class="btn btn-outline-info btn-sm btn-block">
                        <i class="fas fa-th mr-1"></i> 卡片布局
                    </a>
                    {% else %}
                    <a href="{{ url_for('daily_management.inspections_by_date', date_str=today.strftime('%Y-%m-%d'), view='card_layout') }}" class="btn btn-outline-info btn-sm btn-block">
                        <i class="fas fa-th mr-1"></i> 卡片布局
                    </a>
                    {% endif %}
                </div>
                <div class="col-md-2 mb-2">
                    {% if today_log %}
                    <a href="{{ url_for('daily_management.inspections_category_cards', log_id=today_log.id) }}" class="btn btn-outline-warning btn-sm btn-block">
                        <i class="fas fa-layer-group mr-1"></i> 类别卡片
                    </a>
                    {% else %}
                    <a href="{{ url_for('daily_management.inspections_by_date', date_str=today.strftime('%Y-%m-%d'), view='category_cards') }}" class="btn btn-outline-warning btn-sm btn-block">
                        <i class="fas fa-layer-group mr-1"></i> 类别卡片
                    </a>
                    {% endif %}
                </div>
                <div class="col-md-2 mb-2">
                    {% if today_log %}
                    <a href="{{ url_for('daily_management.inspections_simple_table', log_id=today_log.id) }}" class="btn btn-outline-secondary btn-sm btn-block">
                        <i class="fas fa-list mr-1"></i> 简单表格
                    </a>
                    {% else %}
                    <a href="{{ url_for('daily_management.inspections_by_date', date_str=today.strftime('%Y-%m-%d'), view='simple_table') }}" class="btn btn-outline-secondary btn-sm btn-block">
                        <i class="fas fa-list mr-1"></i> 简单表格
                    </a>
                    {% endif %}
                </div>
                <div class="col-md-2 mb-2">
                    <a href="{{ url_for('daily_management.inspection_demo') }}" class="btn btn-outline-dark btn-sm btn-block">
                        <i class="fas fa-play mr-1"></i> 演示页面
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 打印功能 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-info">
                <i class="fas fa-print mr-1"></i> 打印功能
            </h6>
        </div>
        <div class="card-body">
            <p class="text-muted mb-3">打印各类记录和报告</p>
            <div class="row">
                <div class="col-md-3 mb-2">
                    {% if today_log %}
                    <a href="{{ url_for('daily_management.print_log', log_id=today_log.id) }}" class="btn btn-outline-info btn-sm btn-block">
                        <i class="fas fa-file-alt mr-1"></i> 打印日志
                    </a>
                    {% else %}
                    <span class="btn btn-outline-info btn-sm btn-block disabled">
                        <i class="fas fa-file-alt mr-1"></i> 打印日志
                    </span>
                    {% endif %}
                </div>
                <div class="col-md-3 mb-2">
                    {% if today_log %}
                    <a href="{{ url_for('daily_management.print_inspections', log_id=today_log.id) }}" class="btn btn-outline-primary btn-sm btn-block">
                        <i class="fas fa-clipboard-check mr-1"></i> 打印检查记录
                    </a>
                    {% else %}
                    <span class="btn btn-outline-primary btn-sm btn-block disabled">
                        <i class="fas fa-clipboard-check mr-1"></i> 打印检查记录
                    </span>
                    {% endif %}
                </div>
                <div class="col-md-3 mb-2">
                    {% if today_log %}
                    <a href="{{ url_for('daily_management.print_companions', log_id=today_log.id) }}" class="btn btn-outline-success btn-sm btn-block">
                        <i class="fas fa-utensils mr-1"></i> 打印陪餐记录
                    </a>
                    {% else %}
                    <span class="btn btn-outline-success btn-sm btn-block disabled">
                        <i class="fas fa-utensils mr-1"></i> 打印陪餐记录
                    </span>
                    {% endif %}
                </div>
                <div class="col-md-3 mb-2">
                    {% if today_log %}
                    <a href="{{ url_for('daily_management.print_trainings', log_id=today_log.id) }}" class="btn btn-outline-info btn-sm btn-block">
                        <i class="fas fa-chalkboard-teacher mr-1"></i> 打印培训记录
                    </a>
                    {% else %}
                    <span class="btn btn-outline-info btn-sm btn-block disabled">
                        <i class="fas fa-chalkboard-teacher mr-1"></i> 打印培训记录
                    </span>
                    {% endif %}
                </div>
            </div>
            <div class="row">
                <div class="col-md-3 mb-2">
                    {% if today_log %}
                    <a href="{{ url_for('daily_management.print_events', log_id=today_log.id) }}" class="btn btn-outline-danger btn-sm btn-block">
                        <i class="fas fa-calendar-times mr-1"></i> 打印特殊事件
                    </a>
                    {% else %}
                    <span class="btn btn-outline-danger btn-sm btn-block disabled">
                        <i class="fas fa-calendar-times mr-1"></i> 打印特殊事件
                    </span>
                    {% endif %}
                </div>
                <div class="col-md-3 mb-2">
                    {% if today_log %}
                    <a href="{{ url_for('daily_management.print_issues', log_id=today_log.id) }}" class="btn btn-outline-warning btn-sm btn-block">
                        <i class="fas fa-exclamation-triangle mr-1"></i> 打印问题记录
                    </a>
                    {% else %}
                    <span class="btn btn-outline-warning btn-sm btn-block disabled">
                        <i class="fas fa-exclamation-triangle mr-1"></i> 打印问题记录
                    </span>
                    {% endif %}
                </div>
                <div class="col-md-3 mb-2">
                    {% if today_log %}
                    <a href="{{ url_for('daily_management.print_inspection_photos', log_id=today_log.id) }}" class="btn btn-outline-secondary btn-sm btn-block">
                        <i class="fas fa-camera mr-1"></i> 打印检查照片
                    </a>
                    {% else %}
                    <span class="btn btn-outline-secondary btn-sm btn-block disabled">
                        <i class="fas fa-camera mr-1"></i> 打印检查照片
                    </span>
                    {% endif %}
                </div>
                <div class="col-md-3 mb-2">
                    <a href="{{ url_for('daily_management.print_daily_summary', date=today.strftime('%Y-%m-%d')) if today_log else '#' }}"
                       class="btn btn-outline-dark btn-sm btn-block {{ 'disabled' if not today_log else '' }}">
                        <i class="fas fa-file-pdf mr-1"></i> 打印日报汇总
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 高级功能和工具 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-dark">
                <i class="fas fa-tools mr-1"></i> 高级功能和工具
            </h6>
        </div>
        <div class="card-body">
            <p class="text-muted mb-3">高级功能、演示页面和管理工具</p>
            <div class="row">
                <div class="col-md-3 mb-2">
                    <a href="{{ url_for('daily_management.inspection_widget_demo') }}" class="btn btn-outline-dark btn-sm btn-block">
                        <i class="fas fa-puzzle-piece mr-1"></i> 检查小组件演示
                    </a>
                </div>
                <div class="col-md-3 mb-2">
                    {% if today_log %}
                    <a href="{{ url_for('daily_management.generate_photo_upload_qrcode', log_id=today_log.id) }}" class="btn btn-outline-secondary btn-sm btn-block">
                        <i class="fas fa-camera-retro mr-1"></i> 照片上传二维码
                    </a>
                    {% else %}
                    <span class="btn btn-outline-secondary btn-sm btn-block disabled">
                        <i class="fas fa-camera-retro mr-1"></i> 照片上传二维码
                    </span>
                    {% endif %}
                </div>
                <div class="col-md-3 mb-2">
                    {% if today_log %}
                    <a href="{{ url_for('daily_management.generate_companion_qrcode_view', log_id=today_log.id) }}" class="btn btn-outline-success btn-sm btn-block">
                        <i class="fas fa-qrcode mr-1"></i> 陪餐二维码
                    </a>
                    {% else %}
                    <span class="btn btn-outline-success btn-sm btn-block disabled">
                        <i class="fas fa-qrcode mr-1"></i> 陪餐二维码
                    </span>
                    {% endif %}
                </div>
                <div class="col-md-3 mb-2">
                    <a href="{{ url_for('daily_management.old_index') }}" class="btn btn-outline-warning btn-sm btn-block">
                        <i class="fas fa-history mr-1"></i> 旧版首页
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 今日统计信息 -->
    {% if today_log %}
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-success">
                <i class="fas fa-chart-bar mr-1"></i> 今日统计信息
            </h6>
        </div>
        <div class="card-body">
            <div class="row text-center">
                <div class="col-md-3">
                    <div class="overview-card bg-primary text-white p-3 rounded">
                        <div class="icon">
                            <i class="fas fa-clipboard-check"></i>
                        </div>
                        <div class="title">检查记录</div>
                        <div class="count">{{ inspection_count }}</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="overview-card bg-success text-white p-3 rounded">
                        <div class="icon">
                            <i class="fas fa-utensils"></i>
                        </div>
                        <div class="title">陪餐记录</div>
                        <div class="count">{{ companion_count }}</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="overview-card bg-info text-white p-3 rounded">
                        <div class="icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="title">待处理问题</div>
                        <div class="count">{{ pending_issues|length }}</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="overview-card bg-warning text-white p-3 rounded">
                        <div class="icon">
                            <i class="fas fa-calendar-day"></i>
                        </div>
                        <div class="title">日志状态</div>
                        <div class="count">已创建</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 最近日志 -->
    {% if recent_logs %}
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-info">
                <i class="fas fa-history mr-1"></i> 最近日志
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="thead-light">
                        <tr>
                            <th>日期</th>
                            <th>检查记录</th>
                            <th>陪餐记录</th>
                            <th>问题记录</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for log in recent_logs %}
                        <tr>
                            <td>
                                <strong>{{ log.log_date.strftime('%Y-%m-%d') }}</strong>
                                {% if log.log_date == today %}
                                <span class="badge badge-primary ml-1">今天</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge badge-primary">{{ log.inspection_count or 0 }}</span>
                            </td>
                            <td>
                                <span class="badge badge-success">{{ log.companion_count or 0 }}</span>
                            </td>
                            <td>
                                <span class="badge badge-warning">{{ log.issue_count or 0 }}</span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{{ url_for('daily_management.edit_log', date_str=log.log_date.strftime('%Y-%m-%d')) }}"
                                       class="btn btn-outline-primary btn-sm" title="编辑日志">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{{ url_for('daily_management.simplified_inspection', log_id=log.id) }}"
                                       class="btn btn-outline-success btn-sm" title="查看检查记录">
                                        <i class="fas fa-clipboard-check"></i>
                                    </a>
                                    <a href="{{ url_for('daily_management.print_log', log_id=log.id) }}"
                                       class="btn btn-outline-info btn-sm" title="打印日志">
                                        <i class="fas fa-print"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 待处理问题 -->
    {% if pending_issues %}
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-warning">
                <i class="fas fa-exclamation-triangle mr-1"></i> 待处理问题
            </h6>
        </div>
        <div class="card-body">
            <div class="issue-list">
                {% for issue in pending_issues %}
                <div class="issue-item priority-{{ issue.priority or 'medium' }}">
                    <div class="issue-title">{{ issue.issue_type }}</div>
                    <div class="issue-meta">
                        <i class="fas fa-clock mr-1"></i> {{ issue.found_time.strftime('%Y-%m-%d %H:%M') }}
                        {% if issue.responsible_person %}
                        <i class="fas fa-user ml-2 mr-1"></i> {{ issue.responsible_person }}
                        {% endif %}
                    </div>
                    <div class="mt-2">
                        <small>{{ issue.description[:100] }}{% if issue.description|length > 100 %}...{% endif %}</small>
                    </div>
                    <div class="mt-2">
                        <a href="{{ url_for('daily_management.view_issue', issue_id=issue.id) }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye mr-1"></i> 查看详情
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    $(document).ready(function() {
        // 初始化提示工具
        $('[data-toggle="tooltip"]').tooltip();
    });
</script>
{% endblock %}
