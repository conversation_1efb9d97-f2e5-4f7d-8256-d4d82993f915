# 食堂日常管理模块 - 统一打印样式指南

## 📋 概述

本指南定义了食堂日常管理模块所有打印模板的统一样式标准，确保所有打印文档具有一致的外观和专业性。

## 🎨 设计原则

### 1. **一致性**
- 所有模板使用相同的基础样式
- 统一的颜色方案和字体
- 一致的布局结构

### 2. **专业性**
- 清晰的层次结构
- 适合正式文档的字体和间距
- 专业的签名区域

### 3. **可读性**
- 合适的字体大小和行间距
- 清晰的表格边框和背景色
- 状态和评分的颜色编码

## 🏗️ 模板结构

### 基础模板 (`base_print.html`)

```
页眉区域
├── 学校名称 (可选)
├── 文档标题
├── 文档副标题
└── 文档编号

基本信息区域
├── 日期
├── 管理员
└── 其他信息 (可扩展)

主要内容区域
└── 具体内容

签名区域
├── 检查人/记录人
├── 负责人
└── 审核人

页脚区域
├── 系统名称
├── 打印时间
└── 打印人信息
```

## 🎨 样式规范

### 颜色方案

| 用途 | 颜色 | 说明 |
|------|------|------|
| 正常状态 | `#28a745` (绿色) | 检查正常、优秀评分 |
| 异常状态 | `#dc3545` (红色) | 检查异常、较差评分 |
| 待处理 | `#ffc107` (黄色) | 未检查、一般评分 |
| 处理中 | `#17a2b8` (蓝色) | 良好评分 |
| 主要文本 | `#000` (黑色) | 标题、正文 |
| 次要文本 | `#6c757d` (灰色) | 说明、注释 |

### 字体规范

| 元素 | 字体大小 | 字重 |
|------|----------|------|
| 学校名称 | 16pt | bold |
| 文档标题 | 20pt | bold |
| 文档副标题 | 14pt | normal |
| 章节标题 | 16pt | bold |
| 正文 | 12pt | normal |
| 表格内容 | 11pt | normal |
| 页脚信息 | 10pt | normal |

### 间距规范

| 元素 | 间距 |
|------|------|
| 页面边距 | 2cm |
| 章节间距 | 25px |
| 段落间距 | 15px |
| 表格行高 | 1.6 |
| 签名区域高度 | 40px |

## 📊 状态显示标准

### 检查状态

```html
<!-- 正常 -->
<span class="status-normal">✓ 正常</span>

<!-- 异常 -->
<span class="status-abnormal">✗ 异常</span>

<!-- 未检查 -->
<span class="status-pending">- 未检查</span>
```

### 评分显示

```html
<!-- 优秀 (4.5-5.0) -->
<span class="rating rating-excellent">4.8分 (优秀)</span>

<!-- 良好 (3.5-4.4) -->
<span class="rating rating-good">4.0分 (良好)</span>

<!-- 一般 (2.5-3.4) -->
<span class="rating rating-average">3.0分 (一般)</span>

<!-- 较差 (1.0-2.4) -->
<span class="rating rating-poor">2.0分 (较差)</span>
```

## 📝 模板使用指南

### 1. 创建新的打印模板

```html
{% extends 'daily_management/print/base_print.html' %}

{% block title %}文档标题{% endblock %}

{% block document_title %}具体文档名称{% endblock %}

{% block document_subtitle %}日期或其他副标题{% endblock %}

{% block additional_info %}
<!-- 额外的基本信息 -->
{% endblock %}

{% block content %}
<!-- 主要内容 -->
{% endblock %}

{% block signature %}
<!-- 自定义签名区域（可选） -->
{% endblock %}
```

### 2. 表格标准格式

```html
<table>
    <thead>
        <tr>
            <th width="20%">列标题1</th>
            <th width="50%">列标题2</th>
            <th width="30%">列标题3</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>内容1</td>
            <td>内容2</td>
            <td>内容3</td>
        </tr>
    </tbody>
</table>
```

### 3. 照片容器

```html
<div class="photo-container">
    <div class="photo-item">
        <img src="..." alt="照片描述">
        <div class="photo-caption">照片说明</div>
    </div>
</div>
```

## 🔧 技术特性

### 打印优化

- A4 纸张尺寸优化
- 2cm 页边距
- 打印时自动隐藏不必要元素
- 分页控制

### 响应式设计

- 移动设备友好
- 自适应布局
- 灵活的网格系统

### 交互功能

- 一键打印按钮
- 自动打印支持 (`?auto_print=1`)
- 打印前后事件处理

## 📋 检查清单

在创建或修改打印模板时，请确保：

- [ ] 继承自 `base_print.html`
- [ ] 使用统一的状态和评分样式
- [ ] 表格使用标准格式
- [ ] 照片使用 `photo-container` 布局
- [ ] 签名区域使用 `signature-label` 类
- [ ] 无数据时显示友好提示
- [ ] 测试打印效果
- [ ] 验证响应式布局

## 🚀 最佳实践

1. **保持简洁** - 避免过度装饰，专注于内容
2. **语义化** - 使用有意义的CSS类名
3. **可维护性** - 将样式集中在基础模板中
4. **测试** - 在不同浏览器和设备上测试打印效果
5. **文档** - 为特殊用法添加注释说明

---

*本指南将随着系统发展持续更新，确保打印模板的一致性和专业性。*
