{% macro image_uploader(reference_type, reference_id, title="照片上传") %}
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">{{ title }}</h6>
    </div>
    <div class="card-body">
        <div class="image-gallery" id="image-gallery-{{ reference_type }}-{{ reference_id }}" data-reference-type="{{ reference_type }}" data-reference-id="{{ reference_id }}">
            <!-- 添加图片按钮 -->
            <div class="add-image-container">
                <button type="button" class="add-image-btn">
                    <i class="fas fa-plus"></i>
                    <span>添加图片</span>
                </button>
                <input type="file" class="file-input" style="display: none;" accept="image/*">
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 检查是否已加载图片上传器脚本
        if (typeof ImageUploader === 'undefined') {
            // 加载图片上传器脚本
            const script = document.createElement('script');
            script.src = '/static/js/image_uploader.js';
            script.onload = initImageUploader;
            document.head.appendChild(script);
        } else {
            initImageUploader();
        }
        
        function initImageUploader() {
            const galleryContainer = document.getElementById('image-gallery-{{ reference_type }}-{{ reference_id }}');
            if (galleryContainer) {
                new ImageUploader(galleryContainer, {
                    referenceType: '{{ reference_type }}',
                    referenceId: '{{ reference_id }}',
                    apiBaseUrl: '/daily-management/image-api'
                });
            }
        }
    });
</script>
{% endmacro %}
