<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}打印文档{% endblock %}</title>
    <style>
        /* 统一打印样式 - 食堂日常管理模块 */
        @page {
            size: A4;
            margin: 2cm;
        }

        /* 基础样式 */
        body {
            font-family: SimSun, "宋体", serif;
            font-size: 12pt;
            line-height: 1.6;
            color: #000;
            margin: 0;
            padding: 0;
            background: #fff;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 页眉样式 */
        .header {
            text-align: center;
            margin-bottom: 25px;
            border-bottom: 2px solid #000;
            padding-bottom: 15px;
        }

        .school-name {
            font-size: 16pt;
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .title {
            font-size: 20pt;
            font-weight: bold;
            margin-bottom: 8px;
            color: #000;
        }

        .subtitle {
            font-size: 14pt;
            margin-bottom: 5px;
            color: #555;
        }

        .document-code {
            font-size: 10pt;
            color: #666;
            margin-top: 5px;
        }

        /* 信息区域样式 */
        .info {
            margin-bottom: 25px;
            background-color: #f8f9fa;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
        }

        .info-row {
            display: flex;
            margin-bottom: 8px;
            align-items: center;
        }

        .info-label {
            width: 120px;
            font-weight: bold;
            color: #495057;
        }

        .info-value {
            flex: 1;
            color: #000;
        }

        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 11pt;
        }

        th, td {
            border: 1px solid #000;
            padding: 10px 8px;
            text-align: left;
            vertical-align: top;
        }

        th {
            background-color: #e9ecef;
            font-weight: bold;
            text-align: center;
            color: #495057;
        }

        tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        /* 章节标题样式 */
        .section-title {
            font-size: 16pt;
            font-weight: bold;
            margin: 25px 0 15px 0;
            padding: 8px 0 8px 12px;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
            color: #495057;
        }

        /* 状态样式 */
        .status-normal {
            color: #28a745;
            font-weight: bold;
        }

        .status-abnormal {
            color: #dc3545;
            font-weight: bold;
        }

        .status-pending {
            color: #ffc107;
            font-weight: bold;
        }

        .status-processing {
            color: #17a2b8;
            font-weight: bold;
        }

        .status-resolved {
            color: #28a745;
            font-weight: bold;
        }

        /* 评分样式 */
        .rating {
            font-weight: bold;
        }

        .rating-excellent {
            color: #28a745;
        }

        .rating-good {
            color: #17a2b8;
        }

        .rating-average {
            color: #ffc107;
        }

        .rating-poor {
            color: #dc3545;
        }

        /* 照片容器样式 */
        .photo-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .photo-item {
            text-align: center;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            background-color: #fff;
        }

        .photo-item img {
            max-width: 100%;
            max-height: 150px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .photo-caption {
            font-size: 10pt;
            margin-top: 8px;
            color: #6c757d;
            font-weight: 500;
        }

        /* 文本框样式 */
        .text-box {
            border: 1px solid #dee2e6;
            padding: 15px;
            margin-top: 10px;
            min-height: 80px;
            background-color: #f8f9fa;
            border-radius: 4px;
            line-height: 1.8;
        }

        /* 签名区域样式 */
        .signature {
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
            border-top: 1px solid #dee2e6;
            padding-top: 30px;
        }

        .signature-item {
            text-align: center;
            flex: 1;
            margin: 0 15px;
        }

        .signature-line {
            display: inline-block;
            width: 150px;
            height: 40px;
            border-bottom: 1px solid #000;
            margin-bottom: 8px;
        }

        .signature-label {
            font-size: 11pt;
            color: #495057;
            font-weight: 500;
        }

        /* 页脚样式 */
        .footer {
            margin-top: 30px;
            border-top: 1px solid #dee2e6;
            padding-top: 15px;
            display: flex;
            justify-content: space-between;
            font-size: 10pt;
            color: #6c757d;
        }

        .print-info {
            font-size: 9pt;
            color: #6c757d;
            text-align: right;
            margin-top: 15px;
            font-style: italic;
        }

        /* 分页样式 */
        .page-break {
            page-break-before: always;
        }

        /* 无数据提示样式 */
        .no-data {
            text-align: center;
            color: #6c757d;
            font-style: italic;
            padding: 20px;
            background-color: #f8f9fa;
            border: 1px dashed #dee2e6;
            border-radius: 4px;
        }

        /* 打印时隐藏元素 */
        @media print {
            .no-print {
                display: none !important;
            }
            body {
                padding: 0;
                margin: 0;
            }
            .container {
                width: 100%;
                max-width: none;
                padding: 0;
            }
            .page-break {
                page-break-before: always;
            }
        }

        /* 打印按钮样式 */
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .print-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .print-button i {
            margin-right: 8px;
        }

        /* 响应式调整 */
        @media screen and (max-width: 768px) {
            .container {
                padding: 10px;
            }
            .info-row {
                flex-direction: column;
            }
            .info-label {
                width: auto;
                margin-bottom: 4px;
            }
            .signature {
                flex-direction: column;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <button class="print-button no-print" onclick="window.print()">打印文档</button>

    <div class="container">
        <!-- 页眉区域 -->
        <div class="header">
            <!-- 学校名称 -->
            {% if school and school.name %}
            <div class="school-name">{{ school.name }}</div>
            {% endif %}

            <!-- 文档标题 -->
            <div class="title">{% block document_title %}文档标题{% endblock %}</div>

            <!-- 文档副标题 -->
            <div class="subtitle">{% block document_subtitle %}{% endblock %}</div>

            <!-- 文档编号 -->
            {% block document_code %}
            <div class="document-code">文档编号：{% block doc_number %}{{ log.id if log else 'N/A' }}{% endblock %}</div>
            {% endblock %}
        </div>

        <!-- 基本信息区域 -->
        <div class="info">
            {% block document_info %}
            <div class="info-row">
                <div class="info-label">日期：</div>
                <div class="info-value">{% block info_date %}{{ log.log_date|format_datetime('%Y-%m-%d') if log else '' }}{% endblock %}</div>
            </div>
            <div class="info-row">
                <div class="info-label">管理员：</div>
                <div class="info-value">{% block info_manager %}{{ log.manager or '未设置' if log else '' }}{% endblock %}</div>
            </div>
            {% block additional_info %}{% endblock %}
            {% endblock %}
        </div>

        <!-- 主要内容区域 -->
        <div class="content">
            {% block content %}
            <div class="no-data">暂无内容</div>
            {% endblock %}
        </div>

        <!-- 签名区域 -->
        <div class="signature">
            {% block signature %}
            <div class="signature-item">
                <div class="signature-line"></div>
                <div class="signature-label">检查人</div>
            </div>
            <div class="signature-item">
                <div class="signature-line"></div>
                <div class="signature-label">负责人</div>
            </div>
            <div class="signature-item">
                <div class="signature-line"></div>
                <div class="signature-label">审核人</div>
            </div>
            {% endblock %}
        </div>

        <!-- 页脚区域 -->
        <div class="footer">
            <div>学校食堂日常管理系统</div>
            <div>{{ print_date.strftime('%Y-%m-%d %H:%M') if print_date else moment().format('YYYY-MM-DD HH:mm') }}</div>
        </div>

        <div class="print-info">
            本文档由系统自动生成，打印时间：{{ print_date.strftime('%Y-%m-%d %H:%M:%S') if print_date else moment().format('YYYY-MM-DD HH:mm:ss') }}
            {% if current_user %} | 打印人：{{ current_user.real_name or current_user.username }}{% endif %}
        </div>
    </div>

    <!-- JavaScript for printing -->
    <script>
        // 自动打印功能（可选）
        function autoPrint() {
            if (window.location.search.includes('auto_print=1')) {
                setTimeout(function() {
                    window.print();
                }, 1000);
            }
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            autoPrint();
        });

        // 打印前处理
        window.addEventListener('beforeprint', function() {
            console.log('准备打印...');
        });

        // 打印后处理
        window.addEventListener('afterprint', function() {
            console.log('打印完成');
        });
    </script>
</body>
</html>
