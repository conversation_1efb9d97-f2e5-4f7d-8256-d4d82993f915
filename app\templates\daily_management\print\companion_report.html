{% extends 'daily_management/print/base_print.html' %}

{% block title %}食堂陪餐记录 - {{ log.log_date|format_datetime('%Y-%m-%d') }}{% endblock %}

{% block document_title %}食堂陪餐记录{% endblock %}

{% block document_subtitle %}{{ log.log_date|format_datetime('%Y年%m月%d日') }}{% endblock %}

{% block additional_info %}
<div class="info-row">
    <div class="info-label">就餐人数：</div>
    <div class="info-value">学生：{{ log.student_count or 0 }}人，教师：{{ log.teacher_count or 0 }}人，其他：{{ log.other_count or 0 }}人，共{{ (log.student_count or 0) + (log.teacher_count or 0) + (log.other_count or 0) }}人</div>
</div>
<div class="info-row">
    <div class="info-label">菜单：</div>
    <div class="info-value">
        {% if log.breakfast_menu %}早餐：{{ log.breakfast_menu }}{% if log.lunch_menu or log.dinner_menu %} | {% endif %}{% endif %}
        {% if log.lunch_menu %}午餐：{{ log.lunch_menu }}{% if log.dinner_menu %} | {% endif %}{% endif %}
        {% if log.dinner_menu %}晚餐：{{ log.dinner_menu }}{% endif %}
        {% if not (log.breakfast_menu or log.lunch_menu or log.dinner_menu) %}未设置{% endif %}
    </div>
</div>
{% endblock %}

{% block content %}
<!-- 陪餐记录 -->
<div class="section-title">陪餐人员记录</div>
{% if companions %}
<table>
    <thead>
        <tr>
            <th width="15%">姓名</th>
            <th width="15%">职务</th>
            <th width="15%">陪餐时间</th>
            <th width="15%">陪餐餐次</th>
            <th width="40%">反馈意见</th>
        </tr>
    </thead>
    <tbody>
        {% for companion in companions %}
        <tr>
            <td>{{ companion.companion_name }}</td>
            <td>{{ companion.companion_role }}</td>
            <td>{{ companion.dining_time.strftime('%H:%M') if companion.dining_time else '' }}</td>
            <td>{{ companion.meal_type|replace('breakfast', '早餐')|replace('lunch', '午餐')|replace('dinner', '晚餐') }}</td>
            <td>{{ companion.comments or '' }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<!-- 评分统计 -->
<div class="section-title">评分统计</div>
<table>
    <thead>
        <tr>
            <th width="20%">评分项目</th>
            <th width="20%">平均分</th>
            <th width="60%">评价</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>食品质量</td>
            <td>
                {% set food_quality_sum = 0 %}
                {% set food_quality_count = 0 %}
                {% for companion in companions %}
                    {% if companion.taste_rating %}
                        {% set food_quality_sum = food_quality_sum + companion.taste_rating %}
                        {% set food_quality_count = food_quality_count + 1 %}
                    {% endif %}
                {% endfor %}
                {% set avg_score = (food_quality_sum / food_quality_count)|round(1) if food_quality_count > 0 else 0 %}
                {% if avg_score >= 4.5 %}
                <span class="rating rating-excellent">{{ avg_score }}分 (优秀)</span>
                {% elif avg_score >= 3.5 %}
                <span class="rating rating-good">{{ avg_score }}分 (良好)</span>
                {% elif avg_score >= 2.5 %}
                <span class="rating rating-average">{{ avg_score }}分 (一般)</span>
                {% elif avg_score > 0 %}
                <span class="rating rating-poor">{{ avg_score }}分 (较差)</span>
                {% else %}
                <span class="rating">无评分</span>
                {% endif %}
            </td>
            <td>
                {% for companion in companions %}
                    {% if companion.comments %}
                        <div>{{ companion.companion_name }}: {{ companion.comments }}</div>
                    {% endif %}
                {% endfor %}
            </td>
        </tr>
        <tr>
            <td>卫生状况</td>
            <td>
                {% set hygiene_sum = 0 %}
                {% set hygiene_count = 0 %}
                {% for companion in companions %}
                    {% if companion.hygiene_rating %}
                        {% set hygiene_sum = hygiene_sum + companion.hygiene_rating %}
                        {% set hygiene_count = hygiene_count + 1 %}
                    {% endif %}
                {% endfor %}
                {% set avg_score = (hygiene_sum / hygiene_count)|round(1) if hygiene_count > 0 else 0 %}
                {% if avg_score >= 4.5 %}
                <span class="rating rating-excellent">{{ avg_score }}分 (优秀)</span>
                {% elif avg_score >= 3.5 %}
                <span class="rating rating-good">{{ avg_score }}分 (良好)</span>
                {% elif avg_score >= 2.5 %}
                <span class="rating rating-average">{{ avg_score }}分 (一般)</span>
                {% elif avg_score > 0 %}
                <span class="rating rating-poor">{{ avg_score }}分 (较差)</span>
                {% else %}
                <span class="rating">无评分</span>
                {% endif %}
            </td>
            <td>
                {% for companion in companions %}
                    {% if companion.comments %}
                        <div>{{ companion.companion_name }}: {{ companion.comments }}</div>
                    {% endif %}
                {% endfor %}
            </td>
        </tr>
        <tr>
            <td>服务态度</td>
            <td>
                {% set service_sum = 0 %}
                {% set service_count = 0 %}
                {% for companion in companions %}
                    {% if companion.service_rating %}
                        {% set service_sum = service_sum + companion.service_rating %}
                        {% set service_count = service_count + 1 %}
                    {% endif %}
                {% endfor %}
                {% set avg_score = (service_sum / service_count)|round(1) if service_count > 0 else 0 %}
                {% if avg_score >= 4.5 %}
                <span class="rating rating-excellent">{{ avg_score }}分 (优秀)</span>
                {% elif avg_score >= 3.5 %}
                <span class="rating rating-good">{{ avg_score }}分 (良好)</span>
                {% elif avg_score >= 2.5 %}
                <span class="rating rating-average">{{ avg_score }}分 (一般)</span>
                {% elif avg_score > 0 %}
                <span class="rating rating-poor">{{ avg_score }}分 (较差)</span>
                {% else %}
                <span class="rating">无评分</span>
                {% endif %}
            </td>
            <td>
                {% for companion in companions %}
                    {% if companion.comments %}
                        <div>{{ companion.companion_name }}: {{ companion.comments }}</div>
                    {% endif %}
                {% endfor %}
            </td>
        </tr>
    </tbody>
</table>

<!-- 综合意见 -->
<div class="section-title">综合意见</div>
<div class="text-box">
    {% for companion in companions %}
        {% if companion.comments %}
            <div style="margin-bottom: 12px;">
                <strong>{{ companion.companion_name }}（{{ companion.companion_role }}）：</strong>{{ companion.comments }}
            </div>
        {% endif %}
    {% endfor %}
    {% if not companions or not companions|selectattr('comments')|list %}
        <div style="color: #6c757d; font-style: italic;">暂无意见反馈</div>
    {% endif %}
</div>
{% else %}
<p>暂无陪餐记录</p>
{% endif %}
{% endblock %}

{% block signature %}
<div class="signature-item">
    <div class="signature-line"></div>
    <div class="signature-label">陪餐人员</div>
</div>
<div class="signature-item">
    <div class="signature-line"></div>
    <div class="signature-label">食堂负责人</div>
</div>
<div class="signature-item">
    <div class="signature-line"></div>
    <div class="signature-label">学校负责人</div>
</div>
{% endblock %}
